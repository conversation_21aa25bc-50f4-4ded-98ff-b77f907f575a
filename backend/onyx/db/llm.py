import logging
from sqlalchemy import delete
from sqlalchemy import or_
from sqlalchemy import select
from sqlalchemy import func
from sqlalchemy.orm import Session

from onyx.configs.app_configs import AUTH_TYPE
from onyx.configs.constants import AuthType
from onyx.db.models import CloudEmbeddingProvider as CloudEmbeddingProviderModel
from onyx.db.models import DocumentSet
from onyx.db.models import LLMProvider as LLMProviderModel
from onyx.db.models import LLMProvider__UserGroup
from onyx.db.models import SearchSettings
from onyx.db.models import Tool as ToolModel
from onyx.db.models import User
from onyx.db.models import User__UserGroup
from onyx.db.models import UserGroup
from onyx.db.models import UserTeamDefaultLLMProvider
from onyx.server.manage.embedding.models import CloudEmbeddingProvider
from onyx.server.manage.embedding.models import CloudEmbeddingProviderCreationRequest
from onyx.server.manage.llm.models import FullLLMProvider
from onyx.server.manage.llm.models import LLMProviderUpsertRequest
from shared_configs.enums import EmbeddingProvider
from onyx.auth.schemas import UserRole
logger = logging.getLogger(__name__)

def update_group_llm_provider_relationships__no_commit(
    llm_provider_id: int,
    user_team_ids: list[int] | None,
    db_session: Session,
) -> None:
    # Delete existing relationships
    db_session.query(LLMProvider__UserGroup).filter(
        LLMProvider__UserGroup.llm_provider_id == llm_provider_id
    ).delete(synchronize_session="fetch")

    # Add new relationships from given user_team_ids
    if user_team_ids:
        new_relationships = [
            LLMProvider__UserGroup(
                llm_provider_id=llm_provider_id,
                user_group_id=user_team_id,
            )
            for user_team_id in user_team_ids
        ]
        db_session.add_all(new_relationships)


def upsert_cloud_embedding_provider(
    db_session: Session, provider: CloudEmbeddingProviderCreationRequest
) -> CloudEmbeddingProvider:
    existing_provider = (
        db_session.query(CloudEmbeddingProviderModel)
        .filter_by(provider_type=provider.provider_type)
        .first()
    )
    if existing_provider:
        for key, value in provider.model_dump().items():
            setattr(existing_provider, key, value)
    else:
        new_provider = CloudEmbeddingProviderModel(**provider.model_dump())

        db_session.add(new_provider)
        existing_provider = new_provider
    db_session.commit()
    db_session.refresh(existing_provider)
    return CloudEmbeddingProvider.from_request(existing_provider)


def upsert_llm_provider(
    llm_provider: LLMProviderUpsertRequest,
    db_session: Session,
) -> FullLLMProvider:
    existing_llm_provider = db_session.scalar(
        select(LLMProviderModel).where(func.lower(func.trim(LLMProviderModel.name)) == llm_provider.name.lower().strip())
    )
    # Check if this is an update that could break team dependencies
    if existing_llm_provider:
        # Check if making public provider private would break teams
        if (existing_llm_provider.is_public and not llm_provider.is_public):
            can_make_private, reason = _can_make_provider_private(existing_llm_provider.id, llm_provider.user_teams or [], db_session)
            if not can_make_private:
                raise ValueError(reason)

        # Check if removing team access would break teams
        if existing_llm_provider.id:
            old_team_ids = set(team.id for team in existing_llm_provider.groups)
            new_team_ids = set(llm_provider.user_teams or [])
            removed_team_ids = old_team_ids - new_team_ids

            if removed_team_ids:
                can_remove_access, reason = _can_remove_team_access(existing_llm_provider.id, removed_team_ids, db_session)
                if not can_remove_access:
                    raise ValueError(reason)

    if not existing_llm_provider:
        existing_llm_provider = LLMProviderModel(name=llm_provider.name)
        db_session.add(existing_llm_provider)

    if llm_provider.user_teams:
        with db_session.no_autoflush:
            user_teams = db_session.query(UserGroup).filter(UserGroup.id.in_(llm_provider.user_teams)).all()
    else:
        user_teams = []

    # Store old values for fallback logic
    old_is_public = existing_llm_provider.is_public if existing_llm_provider.id else False
    old_team_ids = set(team.id for team in existing_llm_provider.groups) if existing_llm_provider.id else set()

    existing_llm_provider.provider = llm_provider.provider
    existing_llm_provider.api_key = llm_provider.api_key
    existing_llm_provider.api_base = llm_provider.api_base
    existing_llm_provider.api_version = llm_provider.api_version
    existing_llm_provider.custom_config = llm_provider.custom_config
    existing_llm_provider.default_model_name = llm_provider.default_model_name
    existing_llm_provider.fast_default_model_name = llm_provider.fast_default_model_name
    existing_llm_provider.model_names = llm_provider.model_names
    existing_llm_provider.is_public = llm_provider.is_public
    existing_llm_provider.groups = user_teams
    existing_llm_provider.display_model_names = llm_provider.display_model_names
    existing_llm_provider.deployment_name = llm_provider.deployment_name

    if not existing_llm_provider.id:
        # If its not already in the db, we need to generate an ID by flushing
        db_session.flush()

    # Make sure the relationship table stays up to date
    update_group_llm_provider_relationships__no_commit(
        llm_provider_id=existing_llm_provider.id,
        user_team_ids=llm_provider.user_teams,
        db_session=db_session,
    )

    # Handle fallback logic for teams that lost access
    if existing_llm_provider.id:
        new_team_ids = set(llm_provider.user_teams or [])
        removed_team_ids = old_team_ids - new_team_ids

        # If provider was made private or teams lost access, set fallback defaults
        if (old_is_public and not llm_provider.is_public) or removed_team_ids:
            _handle_provider_access_changes(existing_llm_provider.id, removed_team_ids, db_session)

    full_llm_provider = FullLLMProvider.from_model(existing_llm_provider)

    db_session.commit()

    return full_llm_provider


def fetch_existing_embedding_providers(
    db_session: Session,
) -> list[CloudEmbeddingProviderModel]:
    return list(db_session.scalars(select(CloudEmbeddingProviderModel)).all())


def fetch_existing_doc_sets(
    db_session: Session, doc_ids: list[int]
) -> list[DocumentSet]:
    return list(
        db_session.scalars(select(DocumentSet).where(DocumentSet.id.in_(doc_ids))).all()
    )


def fetch_existing_tools(db_session: Session, tool_ids: list[int]) -> list[ToolModel]:
    return list(
        db_session.scalars(select(ToolModel).where(ToolModel.id.in_(tool_ids))).all()
    )


def fetch_existing_llm_providers(
    db_session: Session,
) -> list[LLMProviderModel]:
    stmt = select(LLMProviderModel)
    return list(db_session.scalars(stmt).all())


def fetch_existing_llm_providers_for_user(
    db_session: Session,
    user: User | None = None,
    current_team_id: int | None = None,
) -> list[LLMProviderModel]:
    """
    Returns LLM providers available to the user:
    - Admin: all providers (public and private)
    - Team Admin/Basic: public providers and private providers where user is in the user team
    """
    if not user:
        if AUTH_TYPE != AuthType.DISABLED:
            # User is anonymous
            return list(
                db_session.scalars(
                    select(LLMProviderModel).where(
                        LLMProviderModel.is_public == True  # noqa: E712
                    )
                ).all()
            )
        else:
            # If auth is disabled, user has access to all providers
            return fetch_existing_llm_providers(db_session)

    # Admin: all providers
    if user.role == UserRole.ADMIN:
        return fetch_existing_llm_providers(db_session)

    # Team Admin/Basic: public + private where user's current team has access
    stmt = select(LLMProviderModel).distinct()

    if current_team_id:
        # User has a current team - include public providers and private providers for their current team
        access_conditions = or_(
            LLMProviderModel.is_public,
            LLMProviderModel.id.in_(  # Current team has access
                select(LLMProvider__UserGroup.llm_provider_id).where(
                    LLMProvider__UserGroup.user_group_id == current_team_id
                )
            ),
        )
    else:
        # User has no current team - only public providers
        access_conditions = LLMProviderModel.is_public

    stmt = stmt.where(access_conditions)
    return list(db_session.scalars(stmt).all())


def fetch_embedding_provider(
    db_session: Session, provider_type: EmbeddingProvider
) -> CloudEmbeddingProviderModel | None:
    return db_session.scalar(
        select(CloudEmbeddingProviderModel).where(
            CloudEmbeddingProviderModel.provider_type == provider_type
        )
    )


def fetch_default_provider(db_session: Session) -> FullLLMProvider | None:
    """
    Fetch a default provider. Since we no longer have global defaults,
    this returns the first available provider as a fallback.
    For team-specific defaults, use get_user_team_default_llm_provider instead.
    """
    provider_model = db_session.scalar(
        select(LLMProviderModel).order_by(LLMProviderModel.id).limit(1)
    )
    if not provider_model:
        return None
    return FullLLMProvider.from_model(provider_model)


def fetch_provider(db_session: Session, provider_name: str) -> FullLLMProvider | None:
    provider_model = db_session.scalar(
        select(LLMProviderModel).where(func.lower(func.trim(LLMProviderModel.name)) == provider_name.lower().strip())
    )
    if not provider_model:
        return None
    return FullLLMProvider.from_model(provider_model)


def fetch_provider_by_id(db_session: Session, provider_id: int) -> FullLLMProvider | None:
    provider_model = db_session.scalar(
        select(LLMProviderModel).where(LLMProviderModel.id == provider_id)
    )
    if not provider_model:
        return None
    return FullLLMProvider.from_model(provider_model)


def can_user_edit_llm_provider(user: User, provider: FullLLMProvider, current_team_id: int | None = None) -> bool:
    """
    Check if a user can edit/delete an LLM provider.
    - Admin users can edit any provider
    - Team admin users can only edit providers assigned to their teams (not public ones)
    """
    if user.role == UserRole.ADMIN:
        return True

    if user.role in (UserRole.TEAM_ADMIN, UserRole.BASIC):
        # Team admin can only edit private providers assigned to their teams
        if provider.is_public:
            return False  # Cannot edit public providers (created by admin)

        # Check if any of the user's teams are assigned to this provider
        user_team_ids = set([current_team_id])
        provider_team_ids = set(provider.user_teams)
        return bool(user_team_ids.intersection(provider_team_ids))

    return False


def remove_embedding_provider(
    db_session: Session, provider_type: EmbeddingProvider
) -> None:
    db_session.execute(
        delete(SearchSettings).where(SearchSettings.provider_type == provider_type)
    )

    # Delete the embedding provider
    db_session.execute(
        delete(CloudEmbeddingProviderModel).where(
            CloudEmbeddingProviderModel.provider_type == provider_type
        )
    )

    db_session.commit()


def remove_llm_provider(db_session: Session, provider_id: int) -> None:
    # Check if provider can be safely deleted
    can_delete, reason = can_delete_llm_provider(provider_id, db_session)
    if not can_delete:
        raise ValueError(reason)

    # Get provider info before deletion for cleanup
    provider_to_delete = db_session.scalar(
        select(LLMProviderModel).where(LLMProviderModel.id == provider_id)
    )

    if provider_to_delete:
        # Clean up available_llm_models in all personas that reference this provider
        _cleanup_personas_available_models(db_session, provider_to_delete.name, provider_to_delete.provider)

        # Get teams that had this provider as default before deletion
        teams_with_deleted_default = db_session.scalars(
            select(UserTeamDefaultLLMProvider.user_group_id).where(
                UserTeamDefaultLLMProvider.llm_provider_id == provider_id
            )
        ).all()

        # Clean up any team defaults that reference this provider
        db_session.execute(
            delete(UserTeamDefaultLLMProvider).where(
                UserTeamDefaultLLMProvider.llm_provider_id == provider_id
            )
        )

        # Remove LLMProvider's dependent relationships
        db_session.execute(
            delete(LLMProvider__UserGroup).where(
                LLMProvider__UserGroup.llm_provider_id == provider_id
            )
        )

        # Remove LLMProvider
        db_session.execute(
            delete(LLMProviderModel).where(LLMProviderModel.id == provider_id)
        )

        # Commit the deletion first
        db_session.commit()

        for team_id in teams_with_deleted_default:
            try:
                ensure_team_has_fallback_default_after_deletion(
                    team_id, provider_id, db_session
                )
                logger.info(f"Successfully set fallback default for team {team_id}")
            except Exception as e:
                logger.error(f"Failed to set fallback default for team {team_id}: {e}")
                # Don't raise the exception here to avoid breaking the deletion process
                # The team will be left without a default, but the provider deletion will succeed
    else:
        # Provider doesn't exist, just clean up any orphaned references
        db_session.execute(
            delete(UserTeamDefaultLLMProvider).where(
                UserTeamDefaultLLMProvider.llm_provider_id == provider_id
            )
        )
        db_session.execute(
            delete(LLMProvider__UserGroup).where(
                LLMProvider__UserGroup.llm_provider_id == provider_id
            )
        )
        db_session.commit()


def _cleanup_personas_available_models(
    db_session: Session, provider_name: str, provider_type: str
) -> None:
    """Remove models from the deleted provider from all personas' available_llm_models lists."""
    from onyx.db.models import Persona

    # Get all personas that have available_llm_models configured
    personas_with_models = db_session.scalars(
        select(Persona).where(Persona.available_llm_models.isnot(None))
    ).all()

    for persona in personas_with_models:
        if persona.available_llm_models:
            # Filter out models from the deleted provider
            # Format: "provider_name__provider_type__model_name"
            updated_models = [
                model for model in persona.available_llm_models
                if not model.startswith(f"{provider_name}__{provider_type}__")
            ]

            # Update the persona's available_llm_models
            persona.available_llm_models = updated_models if updated_models else []

    # Commit the changes
    db_session.commit()


# Note: Global default provider functions removed - use team-specific defaults instead

def update_default_provider(provider_id: int, db_session: Session) -> None:
    """
    Legacy function for backward compatibility.
    Since global defaults were removed in favor of team-specific defaults,
    this function now does nothing but logs a warning.

    For new code, use set_user_team_default_llm_provider() instead.
    """
    logger.warning(
        f"update_default_provider({provider_id}) called but global defaults are deprecated. "
        "Use set_user_team_default_llm_provider() for team-specific defaults instead."
    )
    # Do nothing - global defaults are no longer supported


def get_user_team_default_llm_provider(
    user_team_id: int, db_session: Session
) -> LLMProviderModel | None:
    """
    Get the default LLM provider for a specific user team.
    Validates that the default provider is actually available to the team.
    If the stored default is not available, it will be cleaned up and None returned.
    """
    default_provider_relation = db_session.scalar(
        select(UserTeamDefaultLLMProvider).where(
            UserTeamDefaultLLMProvider.user_group_id == user_team_id
        )
    )
    if default_provider_relation:
        # Get the provider
        default_provider = db_session.scalar(
            select(LLMProviderModel).where(
                LLMProviderModel.id == default_provider_relation.llm_provider_id
            )
        )

        if default_provider:
            # Validate that this provider is actually available to the team
            available_providers = get_available_llm_providers_for_team(user_team_id, db_session)
            available_provider_ids = [p.id for p in available_providers]

            if default_provider.id in available_provider_ids:
                return default_provider
            else:
                # The stored default provider is not available to this team anymore
                # Clean up the invalid default
                logger.warning(f"Team {user_team_id} has invalid default provider {default_provider.id} ({default_provider.name}). Cleaning up.")

                db_session.execute(
                    delete(UserTeamDefaultLLMProvider).where(
                        UserTeamDefaultLLMProvider.user_group_id == user_team_id,
                        UserTeamDefaultLLMProvider.llm_provider_id == default_provider.id
                    )
                )
                db_session.commit()
                return None
        else:
            # The provider doesn't exist anymore, clean up the orphaned reference
            logger.warning(f"Team {user_team_id} has orphaned default provider reference {default_provider_relation.llm_provider_id}. Cleaning up.")

            db_session.execute(
                delete(UserTeamDefaultLLMProvider).where(
                    UserTeamDefaultLLMProvider.user_group_id == user_team_id,
                    UserTeamDefaultLLMProvider.llm_provider_id == default_provider_relation.llm_provider_id
                )
            )
            db_session.commit()
            return None

    return None


def set_user_team_default_llm_provider(
    user_team_id: int, llm_provider_id: int, db_session: Session
) -> None:
    """Set the default LLM provider for a specific user team."""
    # Verify that the LLM provider exists
    llm_provider = db_session.scalar(
        select(LLMProviderModel).where(LLMProviderModel.id == llm_provider_id)
    )
    if not llm_provider:
        raise ValueError(f"LLM Provider with id {llm_provider_id} does not exist")

    # Verify that the user group exists
    user_group = db_session.scalar(
        select(UserGroup).where(UserGroup.id == user_team_id)
    )
    if not user_group:
        raise ValueError(f"User group with id {user_team_id} does not exist")

    # Check if the LLM provider is available to this user group using the comprehensive function
    available_providers = get_available_llm_providers_for_team(user_team_id, db_session)
    available_provider_ids = [p.id for p in available_providers]

    if llm_provider_id not in available_provider_ids:
        raise ValueError(
            f"LLM Provider '{llm_provider.name}' (id: {llm_provider_id}) is not available to team '{user_group.name}' (id: {user_team_id}). "
            f"Provider must be public or specifically assigned to the team."
        )

    # Check if a default already exists for this user group
    existing_default = db_session.scalar(
        select(UserTeamDefaultLLMProvider).where(
            UserTeamDefaultLLMProvider.user_group_id == user_team_id
        )
    )

    if existing_default:
        # Update existing default
        existing_default.llm_provider_id = llm_provider_id
    else:
        # Create new default
        new_default = UserTeamDefaultLLMProvider(
            user_group_id=user_team_id,
            llm_provider_id=llm_provider_id,
        )
        db_session.add(new_default)

    db_session.commit()


def get_or_set_fallback_user_team_default_llm_provider(
    user_group_id: int, db_session: Session
) -> LLMProviderModel | None:
    """
    Get the default LLM provider for a user team. If no default is set,
    automatically set the first available LLM provider as default.
    Considers both public providers and team-specific providers.
    """


    # First try to get existing default
    default_provider = get_user_team_default_llm_provider(user_group_id, db_session)
    if default_provider:
        logger.info(f"Team {user_group_id} already has valid default provider: {default_provider.name} (id: {default_provider.id})")
        return default_provider

    logger.info(f"Team {user_group_id} has no valid default provider, setting fallback...")

    # If no default exists, find the first available LLM provider for this team
    # Use the explicit function to ensure we only get providers the team actually has access to
    available_providers = get_available_llm_providers_for_team(user_group_id, db_session)
    logger.info(f"Found {len(available_providers)} available providers for team {user_group_id}: {[p.name for p in available_providers]}")

    # Priority: 1) Team-specific providers, 2) Public providers
    team_specific_providers = [p for p in available_providers if not p.is_public]
    public_providers = [p for p in available_providers if p.is_public]

    # Choose the first team-specific provider, or first public provider if no team-specific ones
    available_provider = None
    if team_specific_providers:
        available_provider = team_specific_providers[0]
        logger.info(f"Selected team-specific provider as fallback: {available_provider.name} (id: {available_provider.id})")
    elif public_providers:
        available_provider = public_providers[0]
        logger.info(f"Selected public provider as fallback: {available_provider.name} (id: {available_provider.id})")

    if available_provider:
        # Set it as default
        logger.info(f"Setting provider {available_provider.name} as default for team {user_group_id}")
        set_user_team_default_llm_provider(
            user_group_id, available_provider.id, db_session
        )
        logger.info(f"Successfully set fallback default provider for team {user_group_id}")
        return available_provider
    else:
        logger.warning(f"No available providers found for team {user_group_id}")

    return None


def get_available_llm_providers_for_team(
    user_group_id: int, db_session: Session
) -> list[LLMProviderModel]:
    """
    Get all LLM providers available to a specific team (both public and team-specific).
    """
    # Get public providers
    public_providers = list(db_session.scalars(
        select(LLMProviderModel)
        .where(LLMProviderModel.is_public == True)  # noqa: E712
        .order_by(LLMProviderModel.id)
    ).all())

    # Get team-specific private providers
    team_providers = list(db_session.scalars(
        select(LLMProviderModel)
        .join(
            LLMProvider__UserGroup,
            LLMProvider__UserGroup.llm_provider_id == LLMProviderModel.id
        )
        .where(
            LLMProvider__UserGroup.user_group_id == user_group_id,
            LLMProviderModel.is_public == False  # noqa: E712
        )
        .order_by(LLMProviderModel.id)
    ).all())

    # Combine and deduplicate (though there shouldn't be duplicates)
    all_providers = public_providers + team_providers
    seen_ids = set()
    unique_providers = []
    for provider in all_providers:
        if provider.id not in seen_ids:
            unique_providers.append(provider)
            seen_ids.add(provider.id)

    return unique_providers


def can_delete_llm_provider(
    provider_id: int, db_session: Session
) -> tuple[bool, str]:
    """
    Check if an LLM provider can be safely deleted.
    Returns (can_delete, reason_if_not).
    """
    # Get the provider
    provider = db_session.scalar(
        select(LLMProviderModel).where(LLMProviderModel.id == provider_id)
    )
    if not provider:
        return False, "Provider not found"

    # Check if this provider is the only available provider for any team
    if provider.is_public:
        # For public providers, check ALL teams (not just those using it as default)
        # because any team might depend on this public provider
        all_teams = db_session.scalars(select(UserGroup.id)).all()

        for team_id in all_teams:
            available_providers = get_available_llm_providers_for_team(team_id, db_session)
            # Filter out the provider we're trying to delete
            remaining_providers = [p for p in available_providers if p.id != provider_id]

            if len(remaining_providers) == 0:
                team = db_session.scalar(
                    select(UserGroup).where(UserGroup.id == team_id)
                )
                team_name = team.name if team else f"Team {team_id}"
                return False, f"Cannot delete provider '{provider.name}' because it's the only LLM provider available to team '{team_name}'. Please assign another provider to the team first."

    else:
        # For private providers, check teams that have specific access
        teams_with_access = db_session.scalars(
            select(LLMProvider__UserGroup.user_group_id)
            .where(LLMProvider__UserGroup.llm_provider_id == provider_id)
        ).all()

        for team_id in teams_with_access:
            available_providers = get_available_llm_providers_for_team(team_id, db_session)
            # Filter out the provider we're trying to delete
            remaining_providers = [p for p in available_providers if p.id != provider_id]

            if len(remaining_providers) == 0:
                team = db_session.scalar(
                    select(UserGroup).where(UserGroup.id == team_id)
                )
                team_name = team.name if team else f"Team {team_id}"
                return False, f"Cannot delete provider '{provider.name}' because it's the only LLM provider available to team '{team_name}'. Please assign another provider to the team first."

    return True, ""


def _can_make_provider_private(
    provider_id: int, new_team_ids: list[int], db_session: Session
) -> tuple[bool, str]:
    """
    Check if a public provider can be made private with the given team assignments.
    Returns (can_make_private, reason_if_not).
    """
    # Get the provider
    provider = db_session.scalar(
        select(LLMProviderModel).where(LLMProviderModel.id == provider_id)
    )
    if not provider:
        return False, "Provider not found"

    # Get all teams that currently use this provider as default
    teams_using_as_default = db_session.scalars(
        select(UserTeamDefaultLLMProvider.user_group_id)
        .where(UserTeamDefaultLLMProvider.llm_provider_id == provider_id)
    ).all()

    new_team_ids_set = set(new_team_ids)

    for team_id in teams_using_as_default:
        # If team won't have access after making private, check if they have alternatives
        if team_id not in new_team_ids_set:
            available_providers = get_available_llm_providers_for_team(team_id, db_session)
            # Filter out the current provider unless it will still be available to this team
            remaining_providers = [
                p for p in available_providers
                if p.id != provider_id or (p.id == provider_id and team_id in new_team_ids_set)
            ]

            if len(remaining_providers) == 0:
                team = db_session.scalar(
                    select(UserGroup).where(UserGroup.id == team_id)
                )
                team_name = team.name if team else f"Team {team_id}"
                return False, f"Cannot make provider '{provider.name}' private because team '{team_name}' would lose access to their only LLM provider. Please assign another provider to the team first."

    return True, ""


def _can_remove_team_access(
    provider_id: int, removed_team_ids: set[int], db_session: Session
) -> tuple[bool, str]:
    """
    Check if team access can be removed from a provider.
    Returns (can_remove, reason_if_not).
    """
    provider = db_session.scalar(
        select(LLMProviderModel).where(LLMProviderModel.id == provider_id)
    )
    if not provider:
        return False, "Provider not found"

    for team_id in removed_team_ids:
        available_providers = get_available_llm_providers_for_team(team_id, db_session)
        # Filter out the provider we're removing access from
        remaining_providers = [p for p in available_providers if p.id != provider_id]

        if len(remaining_providers) == 0:
            team = db_session.scalar(
                select(UserGroup).where(UserGroup.id == team_id)
            )
            team_name = team.name if team else f"Team {team_id}"
            return False, f"Cannot remove team access to provider '{provider.name}' because team '{team_name}' would lose access to their only LLM provider. Please assign another provider to the team first."

    return True, ""


def validate_team_has_available_providers(
    team_id: int, db_session: Session
) -> tuple[bool, str]:
    """
    Validate that a team has at least one available LLM provider.
    Returns (has_providers, reason_if_not).
    """
    available_providers = get_available_llm_providers_for_team(team_id, db_session)

    if len(available_providers) == 0:
        team = db_session.scalar(
            select(UserGroup).where(UserGroup.id == team_id)
        )
        team_name = team.name if team else f"Team {team_id}"
        return False, f"Team '{team_name}' has no available LLM providers. Please assign at least one provider to the team."

    return True, ""


def _handle_provider_access_changes(
    provider_id: int, affected_team_ids: set[int], db_session: Session
) -> None:
    """
    Handle fallback logic when teams lose access to a provider.
    Sets new default providers for affected teams.
    """
    for team_id in affected_team_ids:
        # Check if this team was using the provider as default
        current_default = get_user_team_default_llm_provider(team_id, db_session)
        if current_default and current_default.id == provider_id:
            # Team was using this provider as default, set a fallback
            try:
                fallback_provider = get_or_set_fallback_user_team_default_llm_provider(team_id, db_session)
                if fallback_provider:
                    print(f"Set fallback default provider '{fallback_provider.name}' for team {team_id}")
                else:
                    print(f"No fallback provider available for team {team_id}")
            except Exception as e:
                print(f"Failed to set fallback default for team {team_id}: {e}")


def remove_user_team_default_llm_provider(
    user_group_id: int, db_session: Session
) -> None:
    """Remove the default LLM provider setting for a user team."""
    db_session.execute(
        delete(UserTeamDefaultLLMProvider).where(
            UserTeamDefaultLLMProvider.user_group_id == user_group_id
        )
    )
    db_session.commit()


def ensure_all_teams_have_default_llm_provider(db_session: Session) -> None:
    """
    Ensure all active teams have a default LLM provider.
    If a team doesn't have one, assign the first available provider (public or team-specific).
    """
    # Get all active teams that don't have a default LLM provider
    teams_without_default = db_session.scalars(
        select(UserGroup)
        .where(
            UserGroup.is_up_to_date == True,  # noqa: E712
            UserGroup.is_up_for_deletion == False,  # noqa: E712
            ~UserGroup.id.in_(
                select(UserTeamDefaultLLMProvider.user_group_id)
            )
        )
    ).all()

    for team in teams_without_default:
        # Use the fallback function to set a default
        fallback_provider = get_or_set_fallback_user_team_default_llm_provider(team.id, db_session)
        if fallback_provider:
            print(f"Set default LLM provider '{fallback_provider.name}' for team '{team.name}' (id: {team.id})")
        else:
            print(f"Warning: No available LLM providers for team '{team.name}' (id: {team.id})")


def validate_and_fix_all_team_defaults(db_session: Session) -> dict[str, int]:
    """
    Validate all team default LLM providers and fix any that are invalid.
    Returns a summary of actions taken.
    """

    logger.info("Starting validation and fix of all team default LLM providers")

    # Get all teams with default providers
    teams_with_defaults = db_session.scalars(
        select(UserGroup)
        .join(UserTeamDefaultLLMProvider, UserGroup.id == UserTeamDefaultLLMProvider.user_group_id)
        .where(
            UserGroup.is_up_to_date == True,  # noqa: E712
            UserGroup.is_up_for_deletion == False,  # noqa: E712
        )
    ).all()

    summary = {
        "teams_checked": 0,
        "invalid_defaults_fixed": 0,
        "teams_without_providers": 0
    }

    for team in teams_with_defaults:
        summary["teams_checked"] += 1

        # This will validate the current default and clean it up if invalid
        current_default = get_user_team_default_llm_provider(team.id, db_session)

        if not current_default:
            # The default was invalid and cleaned up, now set a fallback
            fallback_provider = get_or_set_fallback_user_team_default_llm_provider(team.id, db_session)
            if fallback_provider:
                summary["invalid_defaults_fixed"] += 1
            else:
                summary["teams_without_providers"] += 1

    return summary


def ensure_team_has_fallback_default_after_deletion(
    user_group_id: int, deleted_provider_id: int, db_session: Session
) -> None:
    """
    After a default LLM provider is deleted, ensure the team gets a new default.
    This should be called after removing the deleted provider from team defaults.
    """

    # Use the existing function that handles fallback logic
    fallback_provider = get_or_set_fallback_user_team_default_llm_provider(user_group_id, db_session)

    if fallback_provider:
        logger.info(f"Successfully set fallback default LLM provider {fallback_provider.name} (id: {fallback_provider.id}) for team {user_group_id}")
    else:
        raise ValueError(f"No available LLM providers found for team {user_group_id}")


def get_user_default_llm_provider(
    user: "User", db_session: Session, current_team_id: int | None = None
) -> LLMProviderModel | None:
    """
    Get the default LLM provider for a user based on their current team assignment.
    For admin users, returns the first available provider.
    """
    from onyx.auth.schemas import UserRole

    if user.role == UserRole.ADMIN:
        # Admin users don't have teams, return first available provider
        return db_session.scalar(
            select(LLMProviderModel).order_by(LLMProviderModel.id).limit(1)
        )

    # Get user's current team
    if not current_team_id:
        # User has no current team, return None
        return None

    # Get default provider for the current team
    default_provider = get_user_team_default_llm_provider(current_team_id, db_session)
    if default_provider:
        return default_provider

    # No default set, try to get/set fallback for current team
    return get_or_set_fallback_user_team_default_llm_provider(current_team_id, db_session)
