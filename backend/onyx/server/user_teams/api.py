from fastapi import APIRouter
from fastapi import Depends
from fastapi import HTTPException
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session
from pydantic import BaseModel

from onyx.db.user_teams import get_user_teams

from onyx.db.user_teams import create_user_teams
from onyx.db.user_teams import prepare_user_teams_for_deletion
from onyx.db.user_teams import update_user_teams
from onyx.server.user_teams.models import UserTeams
from onyx.server.user_teams.models import CreateUserTeams
from onyx.server.user_teams.models import UpdateUserTeams
from onyx.server.user_teams.models import UserTeamMember
from onyx.auth.users import current_admin_user
from onyx.auth.users import current_curator_or_admin_user
from onyx.auth.users import current_team_admin_or_admin_user
from onyx.auth.users import get_current_team_id_from_header
from onyx.db.engine import get_session
from onyx.db.models import User
from onyx.db.models import User<PERSON>ole
from onyx.utils.logger import setup_logger

logger = setup_logger()

router = APIRouter(prefix="/manage")


@router.get("/admin/user-teams")
def list_user_teams(
    user: User | None = Depends(current_team_admin_or_admin_user),
    current_team_id: int = Depends(get_current_team_id_from_header),
    db_session: Session = Depends(get_session),
    only_up_to_date: bool = False
) -> list[UserTeams]:
    current_user_role = user.role if user else None
    if user is None or current_user_role == UserRole.ADMIN:
        # Admin users can see all user teams
        user_teams = get_user_teams(db_session, only_up_to_date=only_up_to_date)
    else:
        # Team admin users can only see their current team
        if not current_team_id:
            return []
        user_teams = [get_user_teams(db_session, user_team_ids=[current_team_id], only_up_to_date=only_up_to_date)[0]]

    return [UserTeams.from_model(user_team) for user_team in user_teams]

@router.post("/admin/user-teams")
def create_user_team(
    user_team: CreateUserTeams,
    _: User | None = Depends(current_team_admin_or_admin_user),
    db_session: Session = Depends(get_session),
) -> UserTeams:
    try:
        db_user_team = create_user_teams(db_session, user_team)
    except IntegrityError:
        raise HTTPException(
            400,
            f"User team with name '{user_team.name}' already exists. Please "
            + "choose a different name.",
        )
    return UserTeams.from_model(db_user_team)


@router.patch("/admin/user-teams/{user_team_id}")
def patch_user_teams(
    user_team_id: int,
    user_team_update: UpdateUserTeams,
    user: User | None = Depends(current_team_admin_or_admin_user),
    db_session: Session = Depends(get_session),
) -> UserTeams:
    try:
        return UserTeams.from_model(
            update_user_teams(
                db_session=db_session,
                user=user,
                user_team_id=user_team_id,
                user_team_update=user_team_update,
            )
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))


class UpdateUserTeamRoleRequest(BaseModel):
    user_email: str
    new_role: UserRole


@router.patch("/admin/user-teams/{user_team_id}/user-role")
def update_user_role_in_team(
    user_team_id: int,
    role_update: UpdateUserTeamRoleRequest,
    current_user: User | None = Depends(current_team_admin_or_admin_user),
    current_team_id: int = Depends(get_current_team_id_from_header),
    db_session: Session = Depends(get_session),
) -> dict:
    """
    Update a user's role within a specific team.

    - Admin users can update any user's role in any team
    - Team admin users can only update roles for users in their own teams
    """
    from onyx.db.users import get_user_by_email
    from onyx.db.models import User__UserGroup, UserGroup

    # Get the target user
    target_user = get_user_by_email(email=role_update.user_email, db_session=db_session)
    if not target_user:
        raise HTTPException(status_code=404, detail="User not found")

    # Get the team
    team = db_session.query(UserGroup).filter(UserGroup.id == user_team_id).first()
    if not team:
        raise HTTPException(status_code=404, detail="Team not found")

    # Check if user is a member of this team
    user_team_rel = db_session.query(User__UserGroup).filter(
        User__UserGroup.user_id == target_user.id,
        User__UserGroup.user_group_id == user_team_id
    ).first()

    if not user_team_rel:
        raise HTTPException(
            status_code=400,
            detail="User is not a member of this team"
        )

    # Permission checks
    current_user_role = current_user.role
    if current_user_role == UserRole.TEAM_ADMIN:
        # Team admin can only modify users in their current team
        if current_team_id != user_team_id:
            raise HTTPException(
                status_code=403,
                detail="Team admin can only modify roles in their current team"
            )

    # Update the role
    user_team_rel.role = role_update.new_role
    db_session.commit()

    return {
        "message": f"Updated {role_update.user_email}'s role to {role_update.new_role.value} in team {team.name}"
    }

@router.delete("/admin/user-teams/{user_team_id}")
def delete_user_teams(
    user_team_id: int,
    _: User | None = Depends(current_team_admin_or_admin_user),
    db_session: Session = Depends(get_session),
) -> None:
    try:
        prepare_user_teams_for_deletion(db_session, user_team_id)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
