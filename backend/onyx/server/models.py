from typing import Generic
from typing import <PERSON><PERSON>
from typing import TypeV<PERSON>
from uuid import <PERSON><PERSON><PERSON>

from pydantic import BaseModel
from typing import List

from onyx.auth.schemas import UserRole
from onyx.db.models import User


DataT = TypeVar("DataT")


class StatusResponse(BaseModel, Generic[DataT]):
    success: bool
    message: Optional[str] = None
    data: Optional[DataT] = None


class ApiKey(BaseModel):
    api_key: str


class IdReturn(BaseModel):
    id: int


class MinimalUserSnapshot(BaseModel):
    id: UUID
    email: str

class UserIdSnapshot(BaseModel):
    id: UUID

class FullUserSnapshot(BaseModel):
    id: UUID
    email: str
    role: UserRole
    is_active: bool
    password_configured: bool
    status: str  # "active", "inactive", "pending_assignment", "ready_to_signup" - computed dynamically

    @classmethod
    def from_user_model(cls, user: User, requesting_user_current_team_id: int | None = None) -> "FullUserSnapshot":
        user._current_team_id = requesting_user_current_team_id
        # Status is computed dynamically from user state and team assignments
        return cls(
            id=user.id,
            email=user.email,
            role=user.role,
            is_active=user.is_active,
            password_configured=user.password_configured,
            status=user.status,  # Dynamic property that considers role, is_active, and team assignments
        )

class InvitedUserSnapshot(BaseModel):
    email: str
    role: UserRole
    status: str | None = None
    user_id: str | None = None

    @classmethod
    def from_user_model(cls, user: User, requesting_user_current_team_id: int | None = None) -> "InvitedUserSnapshot":
        user._current_team_id = requesting_user_current_team_id
        return cls(
            email=user.email,
            role=user.role,
            status=user.status,
            user_id=str(user.id),
        )


class DisplayPriorityRequest(BaseModel):
    display_priority_map: dict[int, int]

class BulkInviteUserRequest(BaseModel):
    users: List[InvitedUserSnapshot]
