"""migrate pinned assistants

Revision ID: d3e4f5g6h7j1
Revises: d3e4f5g6h7i9
Create Date: 2025-09-22 12:23:21.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy.orm import sessionmaker
from sqlalchemy.sql import table, column
from sqlalchemy import String, Integer
import json

# revision identifiers, used by Alembic.
revision = 'd3e4f5g6h7j1'
down_revision = 'd3e4f5g6h7i9'
branch_labels = None
depends_on = None


def upgrade() -> None:
    bind = op.get_bind()
    Session = sessionmaker(bind=bind)
    session = Session()

    # Add pinned_assistants column to user__user_group
    op.add_column('user__user_group', sa.Column('pinned_assistants', postgresql.JSONB(), nullable=True))

    # Migrate existing pinned_assistants data from "user" to "user__user_group"
    user_table = table(
        'user',
        column('id', postgresql.UUID(as_uuid=True)),
        column('pinned_assistants', postgresql.JSONB(astext_type=String)),
    )
    user_user_group_table = table(
        'user__user_group',
        column('user_id', postgresql.UUID(as_uuid=True)),
        column('user_group_id', Integer),
        column('pinned_assistants', postgresql.JSONB(astext_type=String)),
    )

    # Fetch all users with pinned_assistants
    users_with_pinned_assistants = session.execute(
        sa.select(user_table.c.id, user_table.c.pinned_assistants)
        .where(user_table.c.pinned_assistants.is_not(None))
    ).fetchall()

    for user_id, pinned_assistants in users_with_pinned_assistants:
        # Find the single corresponding user__user_group entry for this user
        # As per clarification, each user is currently part of only one team
        user_group_entry = session.execute(
            sa.select(user_user_group_table.c.user_group_id)
            .where(user_user_group_table.c.user_id == user_id)
        ).fetchone()

        if user_group_entry:
            # Update the pinned_assistants for that specific user__user_group entry
            session.execute(
                user_user_group_table.update()
                .where(
                    (user_user_group_table.c.user_id == user_id) &
                    (user_user_group_table.c.user_group_id == user_group_entry.user_group_id)
                )
                .values(pinned_assistants=pinned_assistants)
            )
    session.commit()

    # Drop pinned_assistants column from "user"
    op.drop_column('user', 'pinned_assistants')


def downgrade() -> None:
    bind = op.get_bind()
    Session = sessionmaker(bind=bind)
    session = Session()

    # Add pinned_assistants column back to "user"
    op.add_column('user', sa.Column('pinned_assistants', postgresql.JSONB(), nullable=True))

    # Migrate pinned_assistants data back from "user__user_group" to "user"
    user_table = table(
        'user',
        column('id', postgresql.UUID(as_uuid=True)),
        column('pinned_assistants', postgresql.JSONB(astext_type=String)),
    )
    user_user_group_table = table(
        'user__user_group',
        column('user_id', postgresql.UUID(as_uuid=True)),
        column('user_group_id', Integer),
        column('pinned_assistants', postgresql.JSONB(astext_type=String)),
    )

    # Fetch all user__user_group entries with pinned_assistants
    user_groups_with_pinned_assistants = session.execute(
        sa.select(user_user_group_table.c.user_id, user_user_group_table.c.pinned_assistants)
        .where(user_user_group_table.c.pinned_assistants.is_not(None))
    ).fetchall()

    for user_id, pinned_assistants in user_groups_with_pinned_assistants:
        # Update the pinned_assistants for the corresponding user
        session.execute(
            user_table.update()
            .where(user_table.c.id == user_id)
            .values(pinned_assistants=pinned_assistants)
        )
    session.commit()

    # Drop pinned_assistants column from "user__user_group"
    op.drop_column('user__user_group', 'pinned_assistants')
